<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Site Error Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        button { margin: 5px; padding: 10px 15px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        input, select { margin: 5px; padding: 5px; }
    </style>
</head>
<body>
    <h1>Add Site Error Test</h1>
    <p>This page helps debug the "[object Object]" error in the add site functionality.</p>

    <div class="test-section">
        <h2>Form Elements Test</h2>
        <div>
            <label>Domain Property:</label>
            <input type="text" id="domainProperty" value="https://test.com/" placeholder="https://example.com/">
        </div>
        <div>
            <label>GA Property ID:</label>
            <input type="text" id="gaPropertyId" value="*********" placeholder="*********">
        </div>
        <div>
            <label>Service Account File:</label>
            <input type="file" id="serviceAccountFile" accept=".json">
        </div>
        <div>
            <label>Homepage (optional):</label>
            <input type="text" id="homepage" placeholder="https://example.com/">
        </div>
        <div>
            <label>WP API Key (optional):</label>
            <input type="text" id="wpApiKey" placeholder="wp-api-key">
        </div>
        <div>
            <label>Start Date (optional):</label>
            <input type="date" id="startDate">
        </div>
        <div>
            <label>End Date (optional):</label>
            <input type="date" id="endDate">
        </div>
        <button onclick="testFormElements()">Test Form Elements</button>
        <button onclick="testFileReading()">Test File Reading</button>
        <button onclick="testAPICall()">Test API Call</button>
        <button onclick="createTestJSON()">Download Test JSON</button>
        <div id="testResult" class="result"></div>
    </div>

    <script>
        const API_ENDPOINT = 'http://localhost:8000';

        function log(message, isError = false) {
            const resultDiv = document.getElementById('testResult');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            
            if (isError) {
                resultDiv.className = 'result error';
            } else {
                resultDiv.className = 'result success';
            }
            
            resultDiv.textContent += logMessage;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('testResult').textContent = '';
        }

        function testFormElements() {
            clearLog();
            log('🔍 Testing form elements...');

            const elements = {
                serviceAccountFile: document.getElementById('serviceAccountFile'),
                domainProperty: document.getElementById('domainProperty'),
                gaPropertyId: document.getElementById('gaPropertyId'),
                homepage: document.getElementById('homepage'),
                wpApiKey: document.getElementById('wpApiKey'),
                startDate: document.getElementById('startDate'),
                endDate: document.getElementById('endDate')
            };

            for (const [name, element] of Object.entries(elements)) {
                if (element) {
                    log(`✅ ${name}: Found, value="${element.value || 'empty'}", type="${element.type}"`);
                } else {
                    log(`❌ ${name}: Not found!`, true);
                }
            }

            const file = elements.serviceAccountFile?.files[0];
            if (file) {
                log(`📁 Selected file: ${file.name} (${file.size} bytes, ${file.type})`);
            } else {
                log('📁 No file selected');
            }
        }

        async function testFileReading() {
            clearLog();
            log('🔍 Testing file reading...');

            const serviceAccountFile = document.getElementById('serviceAccountFile').files[0];
            if (!serviceAccountFile) {
                log('❌ No file selected. Please select a JSON file first.', true);
                return;
            }

            try {
                log(`📁 Reading file: ${serviceAccountFile.name}`);
                
                const content = await new Promise((resolve, reject) => {
                    const fileReader = new FileReader();
                    fileReader.onload = (event) => {
                        log(`📄 File read successfully, length: ${event.target.result.length}`);
                        resolve(event.target.result);
                    };
                    fileReader.onerror = (error) => {
                        log(`❌ File reading error: ${error}`, true);
                        reject(error);
                    };
                    fileReader.readAsText(serviceAccountFile);
                });

                log(`📄 Content preview: ${content.substring(0, 100)}...`);

                const parsed = JSON.parse(content);
                log(`✅ JSON parsed successfully`);
                log(`🔑 Keys found: ${Object.keys(parsed).join(', ')}`);
                log(`📋 Service account type: ${parsed.type || 'unknown'}`);

            } catch (error) {
                log(`❌ Error: ${error.message}`, true);
                log(`❌ Error details: ${JSON.stringify(error)}`, true);
            }
        }

        async function testAPICall() {
            clearLog();
            log('🔍 Testing API call...');

            try {
                // First test form data gathering
                const formData = await gatherFormData();
                if (!formData) {
                    log('❌ Failed to gather form data', true);
                    return;
                }

                log('✅ Form data gathered successfully');
                log(`📋 Domain: ${formData.domain_property}`);
                log(`📋 GA ID: ${formData.ga_property_id}`);
                log(`📋 Service account keys: ${Object.keys(formData.service_account_data).join(', ')}`);

                // Test API call
                log('🚀 Making API call...');
                const response = await fetch(`${API_ENDPOINT}/sites/`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        domain_property: formData.domain_property,
                        ga_property_id: formData.ga_property_id,
                        service_account_data: formData.service_account_data,
                        homepage: formData.homepage,
                        wp_api_key: formData.wp_api_key
                    })
                });

                log(`📡 Response: ${response.status} ${response.statusText}`);

                if (response.ok) {
                    const result = await response.json();
                    log('✅ API call successful!');
                    log(`📋 Result: ${JSON.stringify(result, null, 2)}`);
                } else {
                    const error = await response.json();
                    log(`❌ API error: ${JSON.stringify(error, null, 2)}`, true);
                }

            } catch (error) {
                log(`❌ Error in API test: ${error.message}`, true);
                log(`❌ Error object: ${JSON.stringify(error)}`, true);
                log(`❌ Error stack: ${error.stack}`, true);
            }
        }

        async function gatherFormData() {
            const serviceAccountFile = document.getElementById('serviceAccountFile').files[0];
            if (!serviceAccountFile) {
                log('❌ No service account file selected', true);
                return null;
            }

            const domainProperty = document.getElementById('domainProperty').value;
            const gaPropertyId = document.getElementById('gaPropertyId').value;

            if (!domainProperty || !gaPropertyId) {
                log('❌ Missing required fields', true);
                return null;
            }

            const content = await new Promise((resolve, reject) => {
                const fileReader = new FileReader();
                fileReader.onload = (event) => resolve(event.target.result);
                fileReader.onerror = (error) => reject(error);
                fileReader.readAsText(serviceAccountFile);
            });

            const serviceAccount = JSON.parse(content);

            return {
                domain_property: domainProperty,
                ga_property_id: gaPropertyId,
                service_account_data: serviceAccount,
                homepage: document.getElementById('homepage').value || domainProperty,
                wp_api_key: document.getElementById('wpApiKey').value || null,
                start_date: document.getElementById('startDate').value || null,
                end_date: document.getElementById('endDate').value || null
            };
        }

        function createTestJSON() {
            const testServiceAccount = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

            const dataStr = JSON.stringify(testServiceAccount, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'test-service-account.json';
            link.click();
            
            log('📥 Test JSON file downloaded');
        }

        // Set default dates
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(today.getFullYear() - 1);
            
            document.getElementById('startDate').value = oneYearAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
