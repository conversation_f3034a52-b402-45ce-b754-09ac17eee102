#!/usr/bin/env python3
"""
Script to fix GA data in existing pages by applying the corrected aggregation
"""

import pandas as pd
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.supabase_client import SupabaseClient
from utils.logging import get_logger

logger = get_logger(__name__)

def fix_ga_data_in_pages():
    """Update existing pages with correct GA data"""
    
    print("🔧 Fixing GA Data in Pages Table")
    print("=" * 50)
    
    try:
        # Initialize Supabase client
        import os
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_KEY')
        
        if not supabase_url or not supabase_key:
            print("❌ Missing SUPABASE_URL or SUPABASE_KEY environment variables")
            return False
            
        client = SupabaseClient(supabase_url, supabase_key, "www.lopriore.com")
        client.db_id = 4  # lopriore.com site ID
        
        print("✅ Supabase client initialized")
        
        # Get all GA data
        print("📊 Fetching all GA data...")
        response = client.client.table('ga_data').select('*').eq('site_id', 4).execute()
        
        if not response.data:
            print("❌ No GA data found")
            return False
            
        ga_df = pd.DataFrame(response.data)
        print(f"✅ Fetched {len(ga_df)} GA records")
        
        # Aggregate GA data using the fixed function
        print("🔄 Aggregating GA data...")
        ga_aggregated = client.aggregate_ga_data_by_url(ga_df)
        print(f"✅ Aggregated to {len(ga_aggregated)} unique URLs")
        
        if ga_aggregated.empty:
            print("❌ GA aggregation returned no results")
            return False
        
        # Update pages with GA data
        print("📝 Updating pages with GA data...")
        updated_count = 0
        
        for _, row in ga_aggregated.iterrows():
            url = row['URL']
            page_views = int(row['Google Analytics Page Views']) if pd.notna(row['Google Analytics Page Views']) else 0
            
            # Update the page with this URL
            try:
                update_response = client.client.table('pages').update({
                    'Google Analytics Page Views': page_views
                }).eq('site_id', 4).eq('URL', url).execute()
                
                if update_response.data:
                    updated_count += len(update_response.data)
                    
            except Exception as e:
                print(f"⚠️ Error updating {url}: {e}")
                continue
        
        print(f"✅ Updated {updated_count} pages with GA data")
        
        # Verify the update
        print("🔍 Verifying update...")
        verify_response = client.client.table('pages').select('COUNT(*)').eq('site_id', 4).neq('Google Analytics Page Views', None).execute()
        
        if verify_response.data:
            pages_with_ga = verify_response.data[0]['count']
            print(f"✅ Verification: {pages_with_ga} pages now have GA data")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = fix_ga_data_in_pages()
    if success:
        print("\n🎉 GA data fix completed successfully!")
        print("   Pages now have correct Google Analytics Page Views data.")
        print("   Excel reports will show GA data properly.")
    else:
        print("\n💥 GA data fix failed")
