#!/usr/bin/env python3
"""
Project Cleanup Script

This script reorganizes the project structure according to best practices:
1. Creates proper directory structure
2. Moves files to correct locations
3. Deletes temporary/redundant files (with backup)
4. Updates imports and references
5. Creates a proper .gitignore
6. Updates documentation

IMPORTANT: This script preserves functionality while cleaning up the project.
"""

import os
import shutil
import re
import sys
from pathlib import Path
from datetime import datetime

# Define the project root
PROJECT_ROOT = Path(os.path.dirname(os.path.abspath(__file__)))

# Define directories to create
DIRS_TO_CREATE = [
    "docs",
    "migrations",
    "config",
    "secrets",
    "logs",
    "tests",
    "tests/unit",
    "tests/integration",
    "tests/fixtures",
    "archive"
]

# Define files to move (source_path, destination_path)
FILES_TO_MOVE = [
    # Main entry points - keep in root but create proper ones
    ("api_refactored.py", "main.py"),  # Rename to main.py
    
    # Database migrations
    ("add_gsc_metrics_migration.sql", "migrations/add_gsc_metrics.sql"),
    ("database_migration.sql", "migrations/initial_schema.sql"),
    
    # Configuration files
    ("config_example_no_supabase.json", "config/example.json"),
    ("wp-suites-config.json", "config/wp-suites.json"),
    
    # Service account files (if they exist)
    ("seo-analytics-crawler-fa9eb0c78469.json", "secrets/service-account.json"),
    
    # Documentation files - move all .md files to docs/ except README.md
]

# Documentation files will be moved and then deleted (handled separately)

# Define files to archive (source_path, archive_subdir)
FILES_TO_ARCHIVE = [
    # Debug scripts
    ("debug_clear_data.py", "debug"),
    ("debug_database_save_issue.py", "debug"),
    ("debug_file_upload_issue.py", "debug"),
    ("debug_pages_upsert_issue.py", "debug"),
    ("fix_ga_data_in_pages.py", "debug"),
    
    # Test scripts
    ("test_clear_data.py", "tests"),
    ("test_data_sheet_columns.py", "tests"),
    ("test_data_types_fix.py", "tests"),
    ("test_enhanced_reports.py", "tests"),
    ("test_excel_gsc_metrics.py", "tests"),
    ("test_ga_aggregation_fix.py", "tests"),
    ("test_pages_save_debug.py", "tests"),
    ("test_raw_html_removal.py", "tests"),
    ("test_wordpress_pagination.py", "tests"),
    
    # Migration scripts
    ("migrate_database.py", "migration"),
    ("migrate_to_refactored.py", "migration"),
    ("run_migration_direct.py", "migration"),
    ("verify_migration.py", "migration"),
    
    # Test HTML files
    ("test_add_site_error.html", "debug"),
    ("test_frontend_file_upload.html", "debug"),
    
    # Old reports/output
    ("final_report.xlsx", "reports"),
    
    # Redundant files
    ("main_refactored.py", "legacy"),
]

# Define directories to archive (source_path, archive_subdir)
DIRS_TO_ARCHIVE = [
    ("backup_original", "legacy"),
    ("word", "legacy"),
]

# Define directories to delete (after confirming they're temporary)
DIRS_TO_DELETE = [
    "temp",
    "__pycache__",
]

# Define .gitignore content
GITIGNORE_CONTENT = """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
cover/

# Virtual Environment
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
logs/
*.log
temp/
secrets/
reports/
archive/

# Environment variables
.env
.env.local

# Except these files
!requirements.txt
!setup.sh
!start.sh
!render.yaml
!Dockerfile
!.env.example
"""

# Define new main.py content (to replace api_refactored.py)
MAIN_PY_CONTENT = """#!/usr/bin/env python3
\"\"\"
Main entry point for the SEO Analysis Tool
\"\"\"
import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api.app import app

if __name__ == "__main__":
    import uvicorn
    from src.config.settings import settings
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
"""

# Define updated render.yaml content
RENDER_YAML_CONTENT = """services:
  - type: web
    name: seo-analyzer
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false
      - key: API_HOST
        value: 0.0.0.0
      - key: API_PORT
        fromService:
          type: web
          name: seo-analyzer
          property: port
      - key: PYTHON_VERSION
        value: 3.11.0
"""

def create_directories():
    """Create the necessary directories"""
    print("Creating directories...")
    for dir_path in DIRS_TO_CREATE:
        full_path = PROJECT_ROOT / dir_path
        if not full_path.exists():
            full_path.mkdir(parents=True)
            print(f"  ✅ Created {dir_path}/")
        else:
            print(f"  ℹ️ {dir_path}/ already exists")

def move_files():
    """Move files to their correct locations"""
    print("\nMoving files to proper locations...")
    for source, dest in FILES_TO_MOVE:
        source_path = PROJECT_ROOT / source
        dest_path = PROJECT_ROOT / dest
        
        if not source_path.exists():
            print(f"  ⚠️ {source} not found, skipping")
            continue
            
        # Create parent directory if it doesn't exist
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        # If destination exists, create a backup
        if dest_path.exists() and source_path != dest_path:
            backup_path = dest_path.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d%H%M%S')}")
            shutil.copy2(dest_path, backup_path)
            print(f"  ℹ️ Backed up existing {dest} to {backup_path.name}")
        
        # Special case for api_refactored.py -> main.py
        if source == "api_refactored.py" and dest == "main.py":
            with open(dest_path, 'w') as f:
                f.write(MAIN_PY_CONTENT)
            print(f"  ✅ Created {dest} with updated content")
        else:
            # Regular file move
            try:
                if source_path != dest_path:  # Avoid moving to itself
                    shutil.copy2(source_path, dest_path)
                    print(f"  ✅ Moved {source} → {dest}")
            except Exception as e:
                print(f"  ❌ Error moving {source} to {dest}: {e}")

def archive_files():
    """Archive files that are no longer needed but should be kept"""
    print("\nArchiving files...")
    archive_dir = PROJECT_ROOT / "archive"
    archive_dir.mkdir(exist_ok=True)
    
    for source, subdir in FILES_TO_ARCHIVE:
        source_path = PROJECT_ROOT / source
        if not source_path.exists():
            print(f"  ⚠️ {source} not found, skipping")
            continue
            
        dest_dir = archive_dir / subdir
        dest_dir.mkdir(exist_ok=True)
        dest_path = dest_dir / source_path.name
        
        try:
            shutil.copy2(source_path, dest_path)
            print(f"  ✅ Archived {source} → archive/{subdir}/{source_path.name}")
        except Exception as e:
            print(f"  ❌ Error archiving {source}: {e}")

def archive_directories():
    """Archive directories that are no longer needed but should be kept"""
    print("\nArchiving directories...")
    archive_dir = PROJECT_ROOT / "archive"
    archive_dir.mkdir(exist_ok=True)
    
    for source, subdir in DIRS_TO_ARCHIVE:
        source_path = PROJECT_ROOT / source
        if not source_path.exists():
            print(f"  ⚠️ {source}/ not found, skipping")
            continue
            
        dest_dir = archive_dir / subdir
        dest_dir.mkdir(exist_ok=True)
        dest_path = dest_dir / source_path.name
        
        try:
            if dest_path.exists():
                shutil.rmtree(dest_path)
            shutil.copytree(source_path, dest_path)
            print(f"  ✅ Archived {source}/ → archive/{subdir}/{source_path.name}/")
        except Exception as e:
            print(f"  ❌ Error archiving {source}/: {e}")

def organize_documentation():
    """Move documentation files to docs/ directory"""
    print("\nOrganizing documentation files...")

    # Create docs directory if it doesn't exist
    docs_dir = PROJECT_ROOT / "docs"
    docs_dir.mkdir(exist_ok=True)

    # Get all .md files except README.md and README_REFACTORED.md
    md_files = [f for f in PROJECT_ROOT.glob("*.md")
               if f.name != "README.md" and f.name != "README_REFACTORED.md"]

    # Track which files were moved for later deletion
    moved_files = []

    for md_file in md_files:
        dest_path = docs_dir / md_file.name

        try:
            # Create backup if destination exists
            if dest_path.exists():
                backup_path = dest_path.with_suffix(f".bak.{datetime.now().strftime('%Y%m%d%H%M%S')}")
                shutil.copy2(dest_path, backup_path)
                print(f"  ℹ️ Backed up existing {dest_path.name} to {backup_path.name}")

            # Copy file to docs directory
            shutil.copy2(md_file, dest_path)
            print(f"  ✅ Moved {md_file.name} → docs/{md_file.name}")

            # Add to list for later deletion
            moved_files.append(md_file)

        except Exception as e:
            print(f"  ❌ Error moving {md_file.name}: {e}")

    # Return list of moved files for later deletion
    return moved_files

def update_references():
    """Update references to moved files"""
    print("\nUpdating references...")

    # Update render.yaml
    render_yaml_path = PROJECT_ROOT / "render.yaml"
    if render_yaml_path.exists():
        with open(render_yaml_path, 'w') as f:
            f.write(RENDER_YAML_CONTENT)
        print(f"  ✅ Updated render.yaml to use main.py")

    # Update references in Python files
    for py_file in PROJECT_ROOT.glob("src/**/*.py"):
        with open(py_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Replace references to API_DOCUMENTATION.md
        new_content = content.replace(
            'with open("API_DOCUMENTATION.md", "r", encoding="utf-8") as f:',
            'with open("docs/API_DOCUMENTATION.md", "r", encoding="utf-8") as f:'
        )

        if new_content != content:
            with open(py_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"  ✅ Updated references in {py_file.relative_to(PROJECT_ROOT)}")

    # Update README.md with new instructions
    readme_path = PROJECT_ROOT / "README.md"
    if not readme_path.exists() and (PROJECT_ROOT / "README_REFACTORED.md").exists():
        shutil.copy2(PROJECT_ROOT / "README_REFACTORED.md", readme_path)
        print(f"  ✅ Created README.md from README_REFACTORED.md")

    # Update public/README.md
    public_readme_path = PROJECT_ROOT / "public" / "README.md"
    if public_readme_path.exists():
        with open(public_readme_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Replace references to api_refactored.py and main_refactored.py
        new_content = content.replace("python api_refactored.py", "python main.py")
        new_content = new_content.replace("python main_refactored.py", "python main.py")

        if new_content != content:
            with open(public_readme_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"  ✅ Updated references in public/README.md")

def cleanup_temporary_files():
    """Clean up temporary files and directories"""
    print("\nCleaning up temporary files...")

    # Delete temporary directories
    for dir_name in DIRS_TO_DELETE:
        dir_path = PROJECT_ROOT / dir_name
        if dir_path.exists():
            try:
                shutil.rmtree(dir_path)
                print(f"  ✅ Deleted {dir_name}/")
            except Exception as e:
                print(f"  ❌ Error deleting {dir_name}/: {e}")

    # Delete log files
    log_files = list(PROJECT_ROOT.glob("*.log"))
    for log_file in log_files:
        try:
            log_file.unlink()
            print(f"  ✅ Deleted {log_file.name}")
        except Exception as e:
            print(f"  ❌ Error deleting {log_file.name}: {e}")

    # Delete old reports directory (move to archive first if needed)
    reports_dir = PROJECT_ROOT / "reports"
    if reports_dir.exists():
        archive_reports_dir = PROJECT_ROOT / "archive" / "reports"
        archive_reports_dir.mkdir(parents=True, exist_ok=True)

        try:
            # Move reports to archive
            for item in reports_dir.iterdir():
                dest_path = archive_reports_dir / item.name
                if item.is_dir():
                    if dest_path.exists():
                        shutil.rmtree(dest_path)
                    shutil.copytree(item, dest_path)
                else:
                    shutil.copy2(item, dest_path)

            # Delete original reports directory
            shutil.rmtree(reports_dir)
            print(f"  ✅ Moved reports/ to archive/reports/ and cleaned up")
        except Exception as e:
            print(f"  ❌ Error cleaning up reports/: {e}")

    # Delete test_output directory
    test_output_dir = PROJECT_ROOT / "test_output"
    if test_output_dir.exists():
        try:
            shutil.rmtree(test_output_dir)
            print(f"  ✅ Deleted test_output/")
        except Exception as e:
            print(f"  ❌ Error deleting test_output/: {e}")

def delete_archived_originals(moved_docs=None):
    """Delete original files that have been archived"""
    print("\nDeleting original files that have been archived...")

    # Delete moved documentation files
    if moved_docs:
        for doc_file in moved_docs:
            if doc_file.exists():
                try:
                    doc_file.unlink()
                    print(f"  ✅ Deleted {doc_file.name} (moved to docs/)")
                except Exception as e:
                    print(f"  ❌ Error deleting {doc_file.name}: {e}")

    # Delete archived files from original locations
    for source, _ in FILES_TO_ARCHIVE:
        source_path = PROJECT_ROOT / source
        if source_path.exists():
            try:
                source_path.unlink()
                print(f"  ✅ Deleted {source}")
            except Exception as e:
                print(f"  ❌ Error deleting {source}: {e}")

    # Delete archived directories from original locations
    for source, _ in DIRS_TO_ARCHIVE:
        source_path = PROJECT_ROOT / source
        if source_path.exists():
            try:
                shutil.rmtree(source_path)
                print(f"  ✅ Deleted {source}/")
            except Exception as e:
                print(f"  ❌ Error deleting {source}/: {e}")

    # Delete api_refactored.py after creating main.py
    api_refactored_path = PROJECT_ROOT / "api_refactored.py"
    if api_refactored_path.exists():
        try:
            api_refactored_path.unlink()
            print(f"  ✅ Deleted api_refactored.py (replaced with main.py)")
        except Exception as e:
            print(f"  ❌ Error deleting api_refactored.py: {e}")

def create_gitignore():
    """Create a proper .gitignore file"""
    print("\nCreating .gitignore...")
    gitignore_path = PROJECT_ROOT / ".gitignore"

    if gitignore_path.exists():
        with open(gitignore_path, 'r') as f:
            existing_content = f.read()

        # Append our content to existing .gitignore
        with open(gitignore_path, 'w') as f:
            f.write(existing_content + "\n" + GITIGNORE_CONTENT)
        print("  ✅ Updated existing .gitignore")
    else:
        # Create new .gitignore
        with open(gitignore_path, 'w') as f:
            f.write(GITIGNORE_CONTENT)
        print("  ✅ Created new .gitignore")

def main():
    """Main function to run the cleanup script"""
    print("=" * 60)
    print("🧹 SEO Analysis Tool - Project Cleanup Script")
    print("=" * 60)

    # Confirm before proceeding
    print("\nThis script will reorganize your project structure according to best practices.")
    print("It will create backups of important files before modifying them.")
    print("\nThe following actions will be performed:")
    print("1. Create proper directory structure")
    print("2. Move files to correct locations")
    print("3. Archive temporary/redundant files")
    print("4. Update imports and references")
    print("5. Clean up temporary files")
    print("6. Delete original files that have been archived")
    print("7. Create a proper .gitignore")

    confirm = input("\nDo you want to proceed? (y/n): ")
    if confirm.lower() != 'y':
        print("Cleanup cancelled.")
        return

    # Create directories
    create_directories()

    # Organize documentation files
    moved_docs = organize_documentation()

    # Move files
    move_files()

    # Archive files
    archive_files()

    # Archive directories
    archive_directories()

    # Update references
    update_references()

    # Ask before cleaning up temporary files
    print("\n" + "-" * 60)
    print("⚠️ The next steps will delete temporary files and directories.")
    print("All important files have been archived in the archive/ directory.")
    cleanup_confirm = input("Do you want to proceed with cleanup? (y/n): ")

    if cleanup_confirm.lower() == 'y':
        # Clean up temporary files
        cleanup_temporary_files()

        # Delete original files that have been archived
        delete_archived_originals(moved_docs)
    else:
        print("Skipping cleanup steps. You can manually delete files later.")

    # Create .gitignore
    create_gitignore()

    print("\n" + "=" * 60)
    print("✅ Project cleanup completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Review the changes and ensure everything works as expected")
    print("2. Organize tests: python organize_tests.py")
    print("3. Run tests to verify functionality: python test_cleanup.py")
    print("4. Delete archived files if no longer needed (in archive/ directory)")
    print("5. Update documentation if necessary")

    print("\nTo start the application, run:")
    print("  python main.py")

    print("\nTo organize tests, run:")
    print("  python organize_tests.py")

if __name__ == "__main__":
    main()
