# SEO Analysis Tool Requirements
pandas>=1.5.0
beautifulsoup4>=4.11.0
requests>=2.28.0
playwright>=1.30.0
google-api-python-client>=2.70.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.8.0
python-dateutil>=2.8.0
supabase>=1.0.0
openpyxl>=3.0.0  # For Excel report generation
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
markdownify>=0.11.0  # For HTML to markdown conversion
python-dotenv>=1.0.0  # For environment variable management
pydantic>=2.0.0  # For data validation
pydantic-settings>=2.0.0  # For settings management
xlsxwriter
# Authentication dependencies
PyJWT>=2.8.0  # For JWT token handling
passlib[bcrypt]>=1.7.4  # For password hashing
email-validator>=2.0.0  # For email validation
python-multipart>=0.0.6  # For form data handling