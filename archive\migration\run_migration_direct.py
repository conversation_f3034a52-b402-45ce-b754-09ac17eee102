#!/usr/bin/env python3
"""
Direct Migration Script
Runs the database migration using direct Supabase client connection
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings
from supabase import create_client
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_migration_direct():
    """Run migration using direct Supabase client connection"""
    
    if not settings.supabase_url or not settings.supabase_key:
        logger.error("Supabase credentials not configured")
        return False
    
    try:
        # Create Supabase client using the configured credentials
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        logger.info(f"Connecting to Supabase project: {settings.supabase_url}")
        
        # First, check current table structure
        logger.info("Checking current table structure...")
        current_response = client.table('sites').select('*').limit(1).execute()
        
        if current_response.data and len(current_response.data) > 0:
            current_columns = list(current_response.data[0].keys())
            logger.info(f"Current columns: {current_columns}")
            
            # Check which columns are missing
            required_columns = ['domain_property', 'ga_property_id', 'service_account_data', 'homepage', 'last_updated']
            missing_columns = [col for col in required_columns if col not in current_columns]
            
            if missing_columns:
                logger.info(f"Missing columns: {missing_columns}")
                logger.info("Migration is needed!")
            else:
                logger.info("All required columns already exist!")
                return True
        
        # Since we can't run DDL through the REST API, let's try a different approach
        # We'll create a test record to see what happens
        logger.info("Testing if we can insert with new columns...")
        
        # Try to insert a test record with the new columns
        test_data = {
            'domain': 'test-migration-check.com',
            'domain_property': 'https://test-migration-check.com/',
            'ga_property_id': 'test-123',
            'service_account_data': {'test': True},
            'homepage': 'https://test-migration-check.com/',
            'last_updated': '2025-06-30T00:00:00Z'
        }
        
        try:
            test_response = client.table('sites').insert(test_data).execute()
            if test_response.data:
                logger.info("✅ Test insert successful - columns already exist!")
                # Clean up test record
                client.table('sites').delete().eq('domain', 'test-migration-check.com').execute()
                return True
        except Exception as e:
            if "column" in str(e).lower() and "does not exist" in str(e).lower():
                logger.info("❌ Columns don't exist - migration needed")
            else:
                logger.error(f"Test insert failed: {e}")
        
        # If we get here, we need to run the migration but can't do it via REST API
        logger.error("❌ Migration needed but cannot be run via REST API")
        logger.error("The missing columns need to be added manually in Supabase dashboard")
        
        return False
        
    except Exception as e:
        logger.error(f"❌ Migration check failed: {e}")
        return False

def provide_manual_instructions():
    """Provide manual migration instructions"""
    print("\n" + "="*60)
    print("🔧 MANUAL MIGRATION REQUIRED")
    print("="*60)
    print(f"\nYour application is using Supabase project:")
    print(f"URL: {settings.supabase_url}")
    print(f"Project ID: ltrymguxcxzyofxmbutv")
    
    print(f"\nTo fix the 'domain_property column not found' error:")
    print("1. Open https://supabase.com/dashboard")
    print("2. Find and select project 'ltrymguxcxzyofxmbutv'")
    print("3. Go to SQL Editor")
    print("4. Run this SQL:")
    
    print("\n" + "-"*50)
    print("-- Add missing columns to sites table")
    print("ALTER TABLE sites")
    print("ADD COLUMN IF NOT EXISTS domain_property TEXT,")
    print("ADD COLUMN IF NOT EXISTS ga_property_id TEXT,")
    print("ADD COLUMN IF NOT EXISTS service_account_data JSONB,")
    print("ADD COLUMN IF NOT EXISTS homepage TEXT,")
    print("ADD COLUMN IF NOT EXISTS last_updated TIMESTAMPTZ DEFAULT NOW();")
    print("")
    print("-- Update existing records")
    print("UPDATE sites SET last_updated = created_at WHERE last_updated IS NULL;")
    print("-"*50)
    
    print("\n5. Click 'Run' to execute the SQL")
    print("6. Restart your API server: python api_refactored.py")
    print("7. Test 'Edit Configuration' - should now work!")

if __name__ == "__main__":
    print("🔧 Direct Migration Tool")
    print("=" * 50)
    
    success = run_migration_direct()
    
    if not success:
        provide_manual_instructions()
    else:
        print("\n✅ Migration check completed successfully!")
        print("The database appears to be ready for configuration updates.")
        print("\nNext steps:")
        print("1. Restart your API server if needed")
        print("2. Try 'Edit Configuration' on boernevisioncenter.com")
        print("3. The configuration update should now work!")
