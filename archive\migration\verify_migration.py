#!/usr/bin/env python3
"""
Migration Verification Script
Checks if the database migration was successful
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import settings
from supabase import create_client
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def verify_migration():
    """Verify that the database migration was successful"""
    
    if not settings.supabase_url or not settings.supabase_key:
        logger.error("Supabase credentials not configured")
        return False
    
    try:
        # Create Supabase client
        client = create_client(settings.supabase_url, settings.supabase_key)
        
        logger.info("Checking database schema...")
        
        # Try to select all columns including new ones
        response = client.table('sites').select('id, domain, created_at, domain_property, ga_property_id, service_account_data, homepage, last_updated').limit(1).execute()
        
        if response.data is not None:
            logger.info("✅ All required columns are accessible")
            
            # Check existing sites
            sites_response = client.table('sites').select('*').execute()
            if sites_response.data:
                logger.info(f"\nFound {len(sites_response.data)} existing sites:")
                for site in sites_response.data:
                    domain = site.get('domain', 'Unknown')
                    has_config = bool(site.get('domain_property') and site.get('ga_property_id'))
                    has_service_account = bool(site.get('service_account_data'))
                    
                    status = "✅ Configured" if has_config else "⚠️ Needs Configuration"
                    sa_status = "🔑 Has Service Account" if has_service_account else "🔑 No Service Account"
                    
                    logger.info(f"  - {domain}: {status}, {sa_status}")
                    
                    if not has_config:
                        logger.info(f"    → Use 'Edit Configuration' to set up {domain}")
            else:
                logger.info("No sites found in database")
            
            return True
        else:
            logger.error("❌ Could not access sites table")
            return False
            
    except Exception as e:
        if "Could not find" in str(e) and "column" in str(e):
            logger.error("❌ Migration not completed - missing columns")
            logger.error("Please run the manual migration steps in MANUAL_DATABASE_MIGRATION_GUIDE.md")
        else:
            logger.error(f"❌ Error checking migration: {e}")
        return False

def test_configuration_update():
    """Test if configuration update would work"""
    try:
        from src.database.supabase_client import SupabaseClient
        
        # Try to create a client for testing
        client = SupabaseClient(
            url=settings.supabase_url,
            key=settings.supabase_key,
            domain="test.com"
        )
        
        logger.info("✅ SupabaseClient can be created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ SupabaseClient creation failed: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Migration Verification Tool")
    print("=" * 50)
    
    print("\n1. Checking database schema...")
    schema_ok = verify_migration()
    
    print("\n2. Testing SupabaseClient...")
    client_ok = test_configuration_update()
    
    print("\n" + "=" * 50)
    if schema_ok and client_ok:
        print("✅ Migration verification PASSED!")
        print("\nNext steps:")
        print("1. Restart your API server if it's running")
        print("2. Open http://localhost:8000")
        print("3. Try 'Edit Configuration' on boernevisioncenter.com")
        print("4. The configuration update should now work!")
    else:
        print("❌ Migration verification FAILED!")
        print("\nRequired actions:")
        if not schema_ok:
            print("1. Complete the manual database migration")
            print("   → Follow steps in MANUAL_DATABASE_MIGRATION_GUIDE.md")
        if not client_ok:
            print("2. Check your Supabase credentials")
            print("3. Restart your API server")
        print("4. Run this verification script again")
