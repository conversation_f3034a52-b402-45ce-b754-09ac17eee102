"""
Database-backed task management service
"""
import uuid
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from src.database.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task status enumeration"""
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """Task type enumeration"""
    ANALYSIS = "analysis"
    REANALYSIS = "reanalysis"
    REPORT_GENERATION = "report_generation"


class LogLevel(Enum):
    """Log level enumeration"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


@dataclass
class Task:
    """Task data class"""
    id: str
    site_id: Optional[int]
    task_type: str
    status: str
    priority: int
    progress: int
    message: Optional[str]
    config: Optional[Dict[str, Any]]
    result: Optional[Dict[str, Any]]
    error_message: Optional[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    estimated_duration: Optional[int]
    actual_duration: Optional[int]
    retry_count: int
    max_retries: int
    user_session: Optional[str]


class TaskLogger:
    """Task-specific logger that saves to database"""
    
    def __init__(self, task_id: str, user_session: Optional[str] = None):
        self.task_id = task_id
        self.user_session = user_session
        self.supabase = get_supabase_client()
    
    def log(self, level: LogLevel, message: str, details: Optional[Dict[str, Any]] = None):
        """Log a message for this task"""
        try:
            log_data = {
                "task_id": self.task_id,
                "level": level.value,
                "message": message,
                "details": details,
                "created_at": datetime.now().isoformat()
            }
            
            result = self.supabase.table("task_logs").insert(log_data).execute()
            
            # Also log to console for development
            console_logger = logging.getLogger(f"task.{self.task_id}")
            getattr(console_logger, level.value.lower())(f"[{self.task_id}] {message}")
            
        except Exception as e:
            logger.error(f"Failed to save task log: {e}")
    
    def debug(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.log(LogLevel.DEBUG, message, details)
    
    def info(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.log(LogLevel.INFO, message, details)
    
    def warning(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.log(LogLevel.WARNING, message, details)
    
    def error(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.log(LogLevel.ERROR, message, details)


class DatabaseTaskManager:
    """Database-backed task management system"""
    
    def __init__(self):
        self.supabase = get_supabase_client()
    
    def create_task(
        self,
        task_type: TaskType,
        site_id: Optional[int] = None,
        config: Optional[Dict[str, Any]] = None,
        priority: int = 5,
        user_session: Optional[str] = None,
        estimated_duration: Optional[int] = None
    ) -> str:
        """Create a new task and return its ID"""
        try:
            task_id = str(uuid.uuid4())
            
            task_data = {
                "id": task_id,
                "site_id": site_id,
                "task_type": task_type.value,
                "status": TaskStatus.QUEUED.value,
                "priority": priority,
                "progress": 0,
                "message": "Task created",
                "config": config,
                "retry_count": 0,
                "max_retries": 3,
                "user_session": user_session,
                "estimated_duration": estimated_duration,
                "created_at": datetime.now().isoformat()
            }
            
            result = self.supabase.table("tasks").insert(task_data).execute()
            
            # Create task logger and log creation
            task_logger = TaskLogger(task_id, user_session)
            task_logger.info(f"Task created: {task_type.value}", {"config": config})
            
            logger.info(f"Created task {task_id} of type {task_type.value}")
            return task_id
            
        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            raise
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        try:
            result = self.supabase.table("tasks").select("*").eq("id", task_id).execute()
            
            if result.data:
                task_data = result.data[0]
                return Task(
                    id=task_data["id"],
                    site_id=task_data["site_id"],
                    task_type=task_data["task_type"],
                    status=task_data["status"],
                    priority=task_data["priority"],
                    progress=task_data["progress"],
                    message=task_data["message"],
                    config=task_data["config"],
                    result=task_data["result"],
                    error_message=task_data["error_message"],
                    created_at=datetime.fromisoformat(task_data["created_at"]),
                    started_at=datetime.fromisoformat(task_data["started_at"]) if task_data["started_at"] else None,
                    completed_at=datetime.fromisoformat(task_data["completed_at"]) if task_data["completed_at"] else None,
                    estimated_duration=task_data["estimated_duration"],
                    actual_duration=task_data["actual_duration"],
                    retry_count=task_data["retry_count"],
                    max_retries=task_data["max_retries"],
                    user_session=task_data["user_session"]
                )
            return None
            
        except Exception as e:
            logger.error(f"Failed to get task {task_id}: {e}")
            return None
    
    def update_task_progress(
        self,
        task_id: str,
        progress: int,
        message: str,
        result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ):
        """Update task progress"""
        try:
            # Determine status based on progress
            if progress == 100:
                status = TaskStatus.COMPLETED.value
                completed_at = datetime.now().isoformat()
                started_at_update = {}
            elif progress == -1:
                status = TaskStatus.FAILED.value
                progress = 0
                completed_at = datetime.now().isoformat()
                started_at_update = {}
            else:
                status = TaskStatus.RUNNING.value
                completed_at = None
                # Set started_at if this is the first progress update
                started_at_update = {"started_at": datetime.now().isoformat()}
            
            update_data = {
                "status": status,
                "progress": progress,
                "message": message,
                "result": result,
                "error_message": error,
                **started_at_update
            }
            
            if completed_at:
                update_data["completed_at"] = completed_at
                
                # Calculate actual duration if task is completed
                task = self.get_task(task_id)
                if task and task.started_at:
                    duration = (datetime.now() - task.started_at).total_seconds()
                    update_data["actual_duration"] = int(duration)
            
            result = self.supabase.table("tasks").update(update_data).eq("id", task_id).execute()
            
            # Log the progress update
            task_logger = TaskLogger(task_id)
            if error:
                task_logger.error(f"Task failed: {message}", {"error": error})
            elif progress == 100:
                task_logger.info(f"Task completed: {message}", {"result": result})
            else:
                task_logger.info(f"Progress {progress}%: {message}")
            
        except Exception as e:
            logger.error(f"Failed to update task {task_id}: {e}")
    
    def list_tasks(
        self,
        user_session: Optional[str] = None,
        status: Optional[TaskStatus] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Task]:
        """List tasks with optional filtering"""
        try:
            query = self.supabase.table("tasks").select("*")
            
            if user_session:
                query = query.eq("user_session", user_session)
            
            if status:
                query = query.eq("status", status.value)
            
            query = query.order("created_at", desc=True).limit(limit).offset(offset)
            
            result = query.execute()
            
            tasks = []
            for task_data in result.data:
                tasks.append(Task(
                    id=task_data["id"],
                    site_id=task_data["site_id"],
                    task_type=task_data["task_type"],
                    status=task_data["status"],
                    priority=task_data["priority"],
                    progress=task_data["progress"],
                    message=task_data["message"],
                    config=task_data["config"],
                    result=task_data["result"],
                    error_message=task_data["error_message"],
                    created_at=datetime.fromisoformat(task_data["created_at"]),
                    started_at=datetime.fromisoformat(task_data["started_at"]) if task_data["started_at"] else None,
                    completed_at=datetime.fromisoformat(task_data["completed_at"]) if task_data["completed_at"] else None,
                    estimated_duration=task_data["estimated_duration"],
                    actual_duration=task_data["actual_duration"],
                    retry_count=task_data["retry_count"],
                    max_retries=task_data["max_retries"],
                    user_session=task_data["user_session"]
                ))
            
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to list tasks: {e}")
            return []
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task"""
        try:
            update_data = {
                "status": TaskStatus.CANCELLED.value,
                "completed_at": datetime.now().isoformat(),
                "message": "Task cancelled by user"
            }
            
            result = self.supabase.table("tasks").update(update_data).eq("id", task_id).execute()
            
            # Log the cancellation
            task_logger = TaskLogger(task_id)
            task_logger.info("Task cancelled by user")
            
            return len(result.data) > 0
            
        except Exception as e:
            logger.error(f"Failed to cancel task {task_id}: {e}")
            return False
    
    def get_task_logs(self, task_id: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get logs for a specific task"""
        try:
            result = self.supabase.table("task_logs").select("*").eq("task_id", task_id).order("created_at", desc=True).limit(limit).execute()
            return result.data
            
        except Exception as e:
            logger.error(f"Failed to get logs for task {task_id}: {e}")
            return []
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Remove old completed/failed tasks"""
        try:
            cutoff = datetime.now() - timedelta(hours=max_age_hours)
            
            result = self.supabase.table("tasks").delete().in_(
                "status", [TaskStatus.COMPLETED.value, TaskStatus.FAILED.value, TaskStatus.CANCELLED.value]
            ).lt("completed_at", cutoff.isoformat()).execute()
            
            logger.info(f"Cleaned up {len(result.data)} old tasks")
            
        except Exception as e:
            logger.error(f"Failed to cleanup old tasks: {e}")


# Global database task manager instance
task_manager = DatabaseTaskManager()
