<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .file-upload { margin: 10px 0; }
        button { margin: 5px; padding: 10px 15px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h1>File Upload Debug Test</h1>
    <p>This page tests the file upload functionality to identify issues with service account JSON file handling.</p>

    <!-- Test 1: Add Site Style File Upload -->
    <div class="test-section">
        <h2>Test 1: Add Site Style File Upload</h2>
        <div class="file-upload">
            <button type="button" onclick="document.getElementById('addSiteFile').click()">
                Choose Service Account File (Add Site Style)
            </button>
            <input type="file" id="addSiteFile" accept=".json" style="display: none;">
            <div id="addSiteFileName">No file chosen</div>
        </div>
        <button onclick="testAddSiteUpload()">Test Add Site Upload</button>
        <div id="addSiteResult" class="result"></div>
    </div>

    <!-- Test 2: Edit Config Style File Upload -->
    <div class="test-section">
        <h2>Test 2: Edit Config Style File Upload</h2>
        <div class="file-upload">
            <button type="button" onclick="document.getElementById('editConfigFile').click()">
                Choose Service Account File (Edit Config Style)
            </button>
            <input type="file" id="editConfigFile" accept=".json" style="display: none;">
            <div id="editConfigFileName">No file chosen</div>
        </div>
        <button onclick="testEditConfigUpload()">Test Edit Config Upload</button>
        <div id="editConfigResult" class="result"></div>
    </div>

    <!-- Test 3: Direct File Input -->
    <div class="test-section">
        <h2>Test 3: Direct File Input</h2>
        <input type="file" id="directFile" accept=".json">
        <button onclick="testDirectUpload()">Test Direct Upload</button>
        <div id="directResult" class="result"></div>
    </div>

    <!-- Test 4: Create Test JSON -->
    <div class="test-section">
        <h2>Test 4: Create Test JSON File</h2>
        <button onclick="createTestJSON()">Download Test Service Account JSON</button>
        <p><small>Click this button to download a test JSON file you can use for testing uploads.</small></p>
    </div>

    <script>
        // File name display handlers
        document.getElementById('addSiteFile').addEventListener('change', function(e) {
            document.getElementById('addSiteFileName').textContent = e.target.files[0]?.name || 'No file chosen';
        });

        document.getElementById('editConfigFile').addEventListener('change', function(e) {
            document.getElementById('editConfigFileName').textContent = e.target.files[0]?.name || 'No file chosen';
        });

        // Test 1: Add Site Style Upload (mimics getFormData function)
        async function testAddSiteUpload() {
            const resultDiv = document.getElementById('addSiteResult');
            resultDiv.className = 'result';
            resultDiv.textContent = 'Testing...';

            try {
                const serviceAccountFile = document.getElementById('addSiteFile').files[0];
                if (!serviceAccountFile) {
                    throw new Error('Please select a service account file');
                }

                // This mimics the getFormData() function from the main app
                const result = await new Promise((resolve, reject) => {
                    const fileReader = new FileReader();
                    fileReader.onload = function(event) {
                        try {
                            const serviceAccount = JSON.parse(event.target.result);
                            resolve({
                                fileName: serviceAccountFile.name,
                                fileSize: serviceAccountFile.size,
                                serviceAccountKeys: Object.keys(serviceAccount),
                                serviceAccountType: serviceAccount.type || 'unknown',
                                success: true
                            });
                        } catch (error) {
                            reject(new Error('Invalid JSON file: ' + error.message));
                        }
                    };
                    fileReader.onerror = () => reject(new Error('Error reading file'));
                    fileReader.readAsText(serviceAccountFile);
                });

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Add Site Style Upload Successful!</strong><br>
                    File: ${result.fileName}<br>
                    Size: ${result.fileSize} bytes<br>
                    Service Account Type: ${result.serviceAccountType}<br>
                    Keys Found: ${result.serviceAccountKeys.join(', ')}
                `;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Add Site Style Upload Failed!</strong><br>${error.message}`;
            }
        }

        // Test 2: Edit Config Style Upload (mimics saveConfigChanges function)
        async function testEditConfigUpload() {
            const resultDiv = document.getElementById('editConfigResult');
            resultDiv.className = 'result';
            resultDiv.textContent = 'Testing...';

            try {
                const serviceAccountFile = document.getElementById('editConfigFile').files[0];
                
                let serviceAccountData = null;

                // This mimics the saveConfigChanges() function from the main app
                if (serviceAccountFile) {
                    const fileReader = new FileReader();
                    serviceAccountData = await new Promise((resolve, reject) => {
                        fileReader.onload = function(event) {
                            try {
                                resolve(JSON.parse(event.target.result));
                            } catch (error) {
                                reject(new Error('Invalid JSON file: ' + error.message));
                            }
                        };
                        fileReader.onerror = () => reject(new Error('Error reading file'));
                        fileReader.readAsText(serviceAccountFile);
                    });
                } else {
                    // Simulate keeping existing service account
                    serviceAccountData = { _keep_existing: true };
                }

                const result = {
                    fileName: serviceAccountFile?.name || 'No file (keeping existing)',
                    fileSize: serviceAccountFile?.size || 0,
                    serviceAccountKeys: serviceAccountData ? Object.keys(serviceAccountData) : [],
                    serviceAccountType: serviceAccountData?.type || 'placeholder',
                    success: true
                };

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Edit Config Style Upload Successful!</strong><br>
                    File: ${result.fileName}<br>
                    Size: ${result.fileSize} bytes<br>
                    Service Account Type: ${result.serviceAccountType}<br>
                    Keys Found: ${result.serviceAccountKeys.join(', ')}
                `;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Edit Config Style Upload Failed!</strong><br>${error.message}`;
            }
        }

        // Test 3: Direct File Upload
        async function testDirectUpload() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.className = 'result';
            resultDiv.textContent = 'Testing...';

            try {
                const serviceAccountFile = document.getElementById('directFile').files[0];
                if (!serviceAccountFile) {
                    throw new Error('Please select a service account file');
                }

                const fileReader = new FileReader();
                const content = await new Promise((resolve, reject) => {
                    fileReader.onload = (e) => resolve(e.target.result);
                    fileReader.onerror = () => reject(new Error('Error reading file'));
                    fileReader.readAsText(serviceAccountFile);
                });

                const serviceAccount = JSON.parse(content);

                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>✅ Direct Upload Successful!</strong><br>
                    File: ${serviceAccountFile.name}<br>
                    Size: ${serviceAccountFile.size} bytes<br>
                    Content Preview: ${content.substring(0, 100)}...<br>
                    Service Account Type: ${serviceAccount.type || 'unknown'}
                `;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Direct Upload Failed!</strong><br>${error.message}`;
            }
        }

        // Create test JSON file
        function createTestJSON() {
            const testServiceAccount = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

            const dataStr = JSON.stringify(testServiceAccount, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'test-service-account.json';
            link.click();
        }

        // Log any errors to console
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        console.log('File Upload Debug Test page loaded');
    </script>
</body>
</html>
